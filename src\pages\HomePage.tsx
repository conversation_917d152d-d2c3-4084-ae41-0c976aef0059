import React from 'react';

const HomePage: React.FC = () => {
  console.log('HomePage is rendering...');

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #3730a3 100%)',
      color: 'white',
      padding: '2rem',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ marginBottom: '2rem' }}>
        <div style={{
          width: '80px',
          height: '80px',
          background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
          borderRadius: '50%',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '2rem',
          marginBottom: '2rem'
        }}>
          ⭐
        </div>

        <h1 style={{ fontSize: '4rem', margin: '0 0 1rem 0', fontWeight: 'bold' }}>
          TriviaVerse
        </h1>

        <p style={{ fontSize: '1.5rem', marginBottom: '1rem', opacity: 0.8 }}>
          مغامرة الأسئلة اللامحدودة
        </p>

        <p style={{
          fontSize: '1.1rem',
          marginBottom: '3rem',
          opacity: 0.7,
          maxWidth: '600px',
          margin: '0 auto 3rem auto'
        }}>
          انطلق في رحلة معرفية مثيرة عبر عوالم مختلفة من الأسئلة والتحديات.
          اختبر معلوماتك، تنافس مع الأصدقاء، واكسب الإنجازات في أكبر مغامرة تعليمية تفاعلية.
        </p>

        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button style={{
            padding: '1rem 2rem',
            background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            fontSize: '1.1rem',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            ابدأ المغامرة
          </button>

          <button style={{
            padding: '1rem 2rem',
            background: 'transparent',
            color: 'white',
            border: '2px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '0.5rem',
            fontSize: '1.1rem',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            تسجيل الدخول
          </button>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          marginTop: '4rem',
          maxWidth: '1200px',
          margin: '4rem auto 0 auto'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '1rem',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>🌍</div>
            <h3 style={{ fontSize: '1.3rem', marginBottom: '1rem' }}>عوالم متنوعة</h3>
            <p style={{ opacity: 0.8 }}>استكشف 6 عوالم مختلفة من الأسئلة والتحديات</p>
          </div>

          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '1rem',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>👥</div>
            <h3 style={{ fontSize: '1.3rem', marginBottom: '1rem' }}>تحديات جماعية</h3>
            <p style={{ opacity: 0.8 }}>تنافس مع أصدقائك في تحديات مثيرة</p>
          </div>

          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '1rem',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>🏆</div>
            <h3 style={{ fontSize: '1.3rem', marginBottom: '1rem' }}>نظام إنجازات</h3>
            <p style={{ opacity: 0.8 }}>اكسب الشارات والإنجازات واصعد في المستويات</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
