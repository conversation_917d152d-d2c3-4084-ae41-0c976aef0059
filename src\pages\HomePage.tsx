import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Star,
  Globe,
  Users,
  Trophy,
  Zap,
  Brain,
  ArrowLeft
} from 'lucide-react';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Globe,
      title: 'عوالم متنوعة',
      description: 'استكشف 6 عوالم مختلفة من الأسئلة والتحديات'
    },
    {
      icon: Users,
      title: 'تحديات جماعية',
      description: 'تنافس مع أصدقائك في تحديات مثيرة'
    },
    {
      icon: Trophy,
      title: 'نظام إنجازات',
      description: 'اكسب الشارات والإنجازات واصعد في المستويات'
    },
    {
      icon: Zap,
      title: 'سباق الزمن',
      description: 'اختبر سرعة تفكيرك في تحديات زمنية مثيرة'
    },
    {
      icon: Brain,
      title: 'ألغاز ذكية',
      description: 'تحدى عقلك بألغاز يومية متجددة'
    },
    {
      icon: Star,
      title: 'تحديات يومية',
      description: 'سؤال جديد كل يوم مع مكافآت خاصة'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/stars.png')] opacity-20"></div>
        
        <div className="relative max-w-7xl mx-auto px-6 py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full mb-8"
            >
              <Star className="w-10 h-10 text-white" />
            </motion.div>

            <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 font-arabic">
              TriviaVerse
            </h1>
            
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-2xl md:text-3xl text-white/80 mb-4 font-arabic"
            >
              مغامرة الأسئلة اللامحدودة
            </motion.p>
            
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="text-lg text-white/60 mb-12 max-w-2xl mx-auto font-arabic"
            >
              انطلق في رحلة معرفية مثيرة عبر عوالم مختلفة من الأسئلة والتحديات. 
              اختبر معلوماتك، تنافس مع الأصدقاء، واكسب الإنجازات في أكبر مغامرة تعليمية تفاعلية.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Link
                to="/register"
                className="group relative px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-bold rounded-lg text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              >
                <span className="relative z-10 flex items-center space-x-2 rtl:space-x-reverse">
                  <span>ابدأ المغامرة</span>
                  <ArrowLeft className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </Link>

              <Link
                to="/login"
                className="px-8 py-4 border-2 border-white/30 text-white font-bold rounded-lg text-lg hover:bg-white/10 transition-all duration-300"
              >
                تسجيل الدخول
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-black/20">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 font-arabic">
              لماذا TriviaVerse؟
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto font-arabic">
              اكتشف عالماً جديداً من التعلم والمتعة مع مجموعة متنوعة من الميزات المصممة لتحفيز عقلك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="group p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-primary-500/50 transition-all duration-300"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-white mb-3 font-arabic">
                    {feature.title}
                  </h3>
                  
                  <p className="text-white/70 font-arabic">
                    {feature.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="p-8 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-2xl border border-white/20"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 font-arabic">
              هل أنت مستعد للتحدي؟
            </h2>
            
            <p className="text-lg text-white/80 mb-8 font-arabic">
              انضم إلى آلاف اللاعبين واختبر معلوماتك في أكبر مغامرة تعليمية تفاعلية
            </p>

            <Link
              to="/register"
              className="inline-flex items-center space-x-3 rtl:space-x-reverse px-8 py-4 bg-gradient-to-r from-accent-500 to-accent-600 text-white font-bold rounded-lg text-lg hover:scale-105 transition-all duration-300"
            >
              <span>ابدأ الآن مجاناً</span>
              <Star className="w-5 h-5" />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
