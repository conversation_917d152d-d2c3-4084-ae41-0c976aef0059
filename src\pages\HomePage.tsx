import React from 'react';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white">
      {/* Hero Section */}
      <div className="container mx-auto px-6 py-20">
        <div className="text-center">
          {/* Logo/Icon */}
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full mb-8">
            <span className="text-3xl">⭐</span>
          </div>

          {/* Title */}
          <h1 className="text-6xl md:text-8xl font-bold text-white mb-6">
            TriviaVerse
          </h1>

          {/* Subtitle */}
          <p className="text-2xl md:text-3xl text-white/80 mb-4">
            مغامرة الأسئلة اللامحدودة
          </p>

          {/* Description */}
          <p className="text-lg text-white/60 mb-12 max-w-2xl mx-auto">
            انطلق في رحلة معرفية مثيرة عبر عوالم مختلفة من الأسئلة والتحديات.
            اختبر معلوماتك، تنافس مع الأصدقاء، واكسب الإنجازات في أكبر مغامرة تعليمية تفاعلية.
          </p>

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              to="/register"
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold rounded-lg text-lg hover:scale-105 transition-all duration-300"
            >
              ابدأ المغامرة
            </Link>

            <Link
              to="/login"
              className="px-8 py-4 border-2 border-white/30 text-white font-bold rounded-lg text-lg hover:bg-white/10 transition-all duration-300"
            >
              تسجيل الدخول
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-black/20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              لماذا TriviaVerse؟
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              اكتشف عالماً جديداً من التعلم والمتعة مع مجموعة متنوعة من الميزات المصممة لتحفيز عقلك
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🌍</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                عوالم متنوعة
              </h3>
              <p className="text-white/70">
                استكشف 6 عوالم مختلفة من الأسئلة والتحديات
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">👥</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                تحديات جماعية
              </h3>
              <p className="text-white/70">
                تنافس مع أصدقائك في تحديات مثيرة
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🏆</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                نظام إنجازات
              </h3>
              <p className="text-white/70">
                اكسب الشارات والإنجازات واصعد في المستويات
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                سباق الزمن
              </h3>
              <p className="text-white/70">
                اختبر سرعة تفكيرك في تحديات زمنية مثيرة
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                ألغاز ذكية
              </h3>
              <p className="text-white/70">
                تحدى عقلك بألغاز يومية متجددة
              </p>
            </div>

            <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-2xl">⭐</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                تحديات يومية
              </h3>
              <p className="text-white/70">
                سؤال جديد كل يوم مع مكافآت خاصة
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto p-8 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl border border-white/20">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              هل أنت مستعد للتحدي؟
            </h2>

            <p className="text-lg text-white/80 mb-8">
              انضم إلى آلاف اللاعبين واختبر معلوماتك في أكبر مغامرة تعليمية تفاعلية
            </p>

            <Link
              to="/register"
              className="inline-flex items-center space-x-3 rtl:space-x-reverse px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold rounded-lg text-lg hover:scale-105 transition-all duration-300"
            >
              <span>ابدأ الآن مجاناً</span>
              <span>⭐</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
