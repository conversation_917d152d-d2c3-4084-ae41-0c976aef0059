import React from 'react';

const SimpleHome = () => {
  console.log('SimpleHome is rendering...');
  
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#1e3a8a',
      color: 'white',
      padding: '2rem',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
        TriviaVerse
      </h1>
      <p style={{ fontSize: '1.5rem', marginBottom: '2rem' }}>
        مغامرة الأسئلة اللامحدودة
      </p>
      <p style={{ fontSize: '1rem', marginBottom: '2rem' }}>
        إذا رأيت هذا النص، فإن React يعمل بشكل صحيح!
      </p>
      <button 
        style={{
          padding: '1rem 2rem',
          backgroundColor: '#3b82f6',
          color: 'white',
          border: 'none',
          borderRadius: '0.5rem',
          fontSize: '1rem',
          cursor: 'pointer'
        }}
        onClick={() => alert('الزر يعمل!')}
      >
        اختبار الزر
      </button>
    </div>
  );
};

export default SimpleHome;
