import React from 'react';

const TestPage: React.FC = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#1f2937', 
      color: 'white', 
      padding: '2rem',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
        TriviaVerse
      </h1>
      <p style={{ fontSize: '1.5rem', marginBottom: '2rem' }}>
        مغامرة الأسئلة اللامحدودة
      </p>
      <div style={{ marginBottom: '2rem' }}>
        <button style={{
          backgroundColor: '#3b82f6',
          color: 'white',
          padding: '1rem 2rem',
          border: 'none',
          borderRadius: '0.5rem',
          fontSize: '1.2rem',
          margin: '0 1rem',
          cursor: 'pointer'
        }}>
          ابدأ المغامرة
        </button>
        <button style={{
          backgroundColor: '#10b981',
          color: 'white',
          padding: '1rem 2rem',
          border: 'none',
          borderRadius: '0.5rem',
          fontSize: '1.2rem',
          margin: '0 1rem',
          cursor: 'pointer'
        }}>
          تسجيل الدخول
        </button>
      </div>
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '1rem',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <div style={{
          backgroundColor: '#374151',
          padding: '1.5rem',
          borderRadius: '0.5rem'
        }}>
          <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>عوالم متنوعة</h3>
          <p>استكشف 6 عوالم مختلفة من الأسئلة والتحديات</p>
        </div>
        <div style={{
          backgroundColor: '#374151',
          padding: '1.5rem',
          borderRadius: '0.5rem'
        }}>
          <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>تحديات جماعية</h3>
          <p>تنافس مع أصدقائك في تحديات مثيرة</p>
        </div>
        <div style={{
          backgroundColor: '#374151',
          padding: '1.5rem',
          borderRadius: '0.5rem'
        }}>
          <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>نظام إنجازات</h3>
          <p>اكسب الشارات والإنجازات واصعد في المستويات</p>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
