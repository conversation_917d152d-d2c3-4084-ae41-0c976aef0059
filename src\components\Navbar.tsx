import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  User, 
  Settings, 
  LogOut, 
  Trophy, 
  Star,
  Bell
} from 'lucide-react';
import { useAppStore } from '../store';
import { authAPI } from '../services/api';

const Navbar: React.FC = () => {
  const { user, setUser } = useAppStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await authAPI.logout();
      setUser(null);
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!user) return null;

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="bg-black/20 backdrop-blur-md border-b border-white/10 px-6 py-4"
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <Link to="/dashboard" className="flex items-center space-x-3 rtl:space-x-reverse">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center"
          >
            <Star className="w-6 h-6 text-white" />
          </motion.div>
          <div>
            <h1 className="text-xl font-bold text-white">TriviaVerse</h1>
            <p className="text-xs text-white/60">مغامرة الأسئلة اللامحدودة</p>
          </div>
        </Link>

        {/* User Info */}
        <div className="flex items-center space-x-6 rtl:space-x-reverse">
          {/* Level & XP */}
          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse">
            <div className="text-center">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Trophy className="w-4 h-4 text-accent-400" />
                <span className="text-sm font-semibold text-white">
                  المستوى {user.level}
                </span>
              </div>
              <div className="w-24 h-2 bg-white/20 rounded-full mt-1">
                <motion.div
                  className="h-full bg-gradient-to-r from-accent-400 to-accent-600 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ 
                    width: `${((user.xp % 1000) / 1000) * 100}%` 
                  }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-white/60">النقاط الإجمالية</p>
              <p className="text-sm font-bold text-primary-400">
                {user.totalScore.toLocaleString('ar-EG')}
              </p>
            </div>
          </div>

          {/* Notifications */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            className="relative p-2 text-white/70 hover:text-white transition-colors"
          >
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </motion.button>

          {/* User Menu */}
          <div className="relative group">
            <motion.button
              whileHover={{ scale: 1.05 }}
              className="flex items-center space-x-3 rtl:space-x-reverse p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                {user.avatar ? (
                  <img 
                    src={user.avatar} 
                    alt={user.username}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <User className="w-4 h-4 text-white" />
                )}
              </div>
              <div className="hidden md:block text-right">
                <p className="text-sm font-semibold text-white">{user.username}</p>
                <p className="text-xs text-white/60">مرحباً بك</p>
              </div>
            </motion.button>

            {/* Dropdown Menu */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileHover={{ opacity: 1, y: 0 }}
              className="absolute left-0 mt-2 w-48 bg-black/80 backdrop-blur-md rounded-lg border border-white/10 py-2 opacity-0 group-hover:opacity-100 transition-all duration-200 z-50"
            >
              <Link
                to="/profile"
                className="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/10 transition-colors"
              >
                <User className="w-4 h-4" />
                <span>الملف الشخصي</span>
              </Link>
              
              <Link
                to="/achievements"
                className="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/10 transition-colors"
              >
                <Trophy className="w-4 h-4" />
                <span>الإنجازات</span>
              </Link>

              <button
                className="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/10 transition-colors w-full"
              >
                <Settings className="w-4 h-4" />
                <span>الإعدادات</span>
              </button>

              <hr className="my-2 border-white/10" />

              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-colors w-full"
              >
                <LogOut className="w-4 h-4" />
                <span>تسجيل الخروج</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default Navbar;
