import { useCallback } from 'react';
import { soundService, SoundType } from '../services/soundService';
import { useAppStore } from '../store';

export const useSound = () => {
  const { user } = useAppStore();
  
  const playSound = useCallback((soundType: SoundType) => {
    if (user?.preferences.soundEnabled) {
      soundService.play(soundType);
    }
  }, [user?.preferences.soundEnabled]);

  const playSuccess = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playSuccess();
    }
  }, [user?.preferences.soundEnabled]);

  const playError = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playError();
    }
  }, [user?.preferences.soundEnabled]);

  const playClick = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playClick();
    }
  }, [user?.preferences.soundEnabled]);

  const playHover = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playHover();
    }
  }, [user?.preferences.soundEnabled]);

  const playNotification = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playNotification();
    }
  }, [user?.preferences.soundEnabled]);

  const playGameStart = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playGameStart();
    }
  }, [user?.preferences.soundEnabled]);

  const playGameEnd = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playGameEnd();
    }
  }, [user?.preferences.soundEnabled]);

  const playLevelUp = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playLevelUp();
    }
  }, [user?.preferences.soundEnabled]);

  const playTimeWarning = useCallback(() => {
    if (user?.preferences.soundEnabled) {
      soundService.playTimeWarning();
    }
  }, [user?.preferences.soundEnabled]);

  return {
    playSound,
    playSuccess,
    playError,
    playClick,
    playHover,
    playNotification,
    playGameStart,
    playGameEnd,
    playLevelUp,
    playTimeWarning,
    isEnabled: user?.preferences.soundEnabled || false
  };
};

export default useSound;
