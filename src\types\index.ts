// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  level: number;
  xp: number;
  totalScore: number;
  gamesPlayed: number;
  achievements: Achievement[];
  streakDays: number;
  lastLoginDate: string;
  createdAt: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  soundEnabled: boolean;
  musicEnabled: boolean;
  language: 'ar' | 'en';
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  theme: 'nebula' | 'cosmic' | 'galaxy';
}

// Question Types
export interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: QuestionCategory;
  explanation?: string;
  timeLimit: number;
  points: number;
  tags: string[];
  source?: string;
  createdAt: string;
}

export type QuestionCategory = 
  | 'history' 
  | 'science' 
  | 'culture' 
  | 'movies' 
  | 'technology' 
  | 'mystery';

// Game Types
export interface GameSession {
  id: string;
  userId: string;
  mode: GameMode;
  questions: Question[];
  currentQuestionIndex: number;
  answers: Answer[];
  score: number;
  startTime: string;
  endTime?: string;
  status: 'active' | 'completed' | 'abandoned';
  timeRemaining?: number;
}

export type GameMode = 
  | 'daily-challenge'
  | 'trivia-worlds'
  | 'time-rush'
  | 'friend-challenge'
  | 'daily-riddle'
  | 'battle-royale';

export interface Answer {
  questionId: string;
  selectedAnswer: number;
  isCorrect: boolean;
  timeSpent: number;
  attempts: number;
  pointsEarned: number;
  timestamp: string;
}

// World Types
export interface TriviaWorld {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  category: QuestionCategory;
  totalQuestions: number;
  completedQuestions: number;
  isUnlocked: boolean;
  requiredAccuracy: number;
  theme: WorldTheme;
  icon: string;
  backgroundImage: string;
}

export interface WorldTheme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundGradient: string[];
}

// Achievement Types
export interface Achievement {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  icon: string;
  type: AchievementType;
  requirement: number;
  progress: number;
  isUnlocked: boolean;
  unlockedAt?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export type AchievementType = 
  | 'first-win'
  | 'speed-demon'
  | 'perfect-score'
  | 'streak-master'
  | 'world-conqueror'
  | 'knowledge-seeker'
  | 'social-butterfly';

// Leaderboard Types
export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  score: number;
  rank: number;
  gamesPlayed: number;
  accuracy: number;
  level: number;
}

export interface Leaderboard {
  type: 'daily' | 'weekly' | 'monthly' | 'all-time';
  entries: LeaderboardEntry[];
  lastUpdated: string;
}

// Power-up Types
export interface PowerUp {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  type: PowerUpType;
  cost: number;
  duration?: number;
  icon: string;
  rarity: 'common' | 'rare' | 'epic';
}

export type PowerUpType = 
  | 'double-points'
  | 'skip-question'
  | 'time-freeze'
  | 'fifty-fifty'
  | 'extra-life'
  | 'hint';

// Multiplayer Types
export interface MultiplayerRoom {
  id: string;
  hostId: string;
  players: MultiplayerPlayer[];
  maxPlayers: number;
  gameMode: GameMode;
  status: 'waiting' | 'active' | 'finished';
  questions: Question[];
  currentQuestionIndex: number;
  settings: RoomSettings;
  createdAt: string;
}

export interface MultiplayerPlayer {
  userId: string;
  username: string;
  avatar?: string;
  score: number;
  answers: Answer[];
  isReady: boolean;
  isConnected: boolean;
  rank?: number;
}

export interface RoomSettings {
  questionCount: number;
  timePerQuestion: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  categories: QuestionCategory[];
  allowPowerUps: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Store Types
export interface AppState {
  user: User | null;
  currentGame: GameSession | null;
  triviaWorlds: TriviaWorld[];
  achievements: Achievement[];
  leaderboards: Record<string, Leaderboard>;
  powerUps: PowerUp[];
  isLoading: boolean;
  error: string | null;
}

// Event Types
export interface GameEvent {
  type: 'question-answered' | 'game-completed' | 'achievement-unlocked' | 'level-up';
  payload: any;
  timestamp: string;
}
