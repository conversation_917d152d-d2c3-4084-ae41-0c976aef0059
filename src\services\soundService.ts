// Sound Service for TriviaVerse
// This service handles all audio feedback in the application

export type SoundType = 
  | 'click'
  | 'correct'
  | 'incorrect'
  | 'achievement'
  | 'levelUp'
  | 'timeWarning'
  | 'gameStart'
  | 'gameEnd'
  | 'hover'
  | 'notification';

class SoundService {
  private sounds: Map<SoundType, HTMLAudioElement> = new Map();
  private isEnabled: boolean = true;
  private volume: number = 0.5;

  constructor() {
    this.initializeSounds();
  }

  private initializeSounds() {
    // For now, we'll use Web Audio API to generate simple tones
    // In production, these would be actual audio files
    
    const soundConfigs: Record<SoundType, { frequency: number; duration: number; type: OscillatorType }> = {
      click: { frequency: 800, duration: 100, type: 'sine' },
      correct: { frequency: 523, duration: 300, type: 'sine' }, // C5 note
      incorrect: { frequency: 200, duration: 500, type: 'sawtooth' },
      achievement: { frequency: 659, duration: 600, type: 'sine' }, // E5 note
      levelUp: { frequency: 784, duration: 800, type: 'sine' }, // G5 note
      timeWarning: { frequency: 440, duration: 200, type: 'square' }, // A4 note
      gameStart: { frequency: 392, duration: 400, type: 'sine' }, // G4 note
      gameEnd: { frequency: 330, duration: 600, type: 'sine' }, // E4 note
      hover: { frequency: 600, duration: 50, type: 'sine' },
      notification: { frequency: 500, duration: 250, type: 'sine' }
    };

    // Create audio elements for each sound type
    Object.entries(soundConfigs).forEach(([soundType, config]) => {
      const audio = this.createToneAudio(config.frequency, config.duration, config.type);
      this.sounds.set(soundType as SoundType, audio);
    });
  }

  private createToneAudio(frequency: number, duration: number, type: OscillatorType): HTMLAudioElement {
    // Create a simple tone using Web Audio API and convert to audio element
    // This is a placeholder implementation
    const audio = new Audio();
    
    // Create a data URL for a simple beep sound
    // In a real implementation, you would load actual audio files
    const dataUrl = this.generateToneDataUrl(frequency, duration, type);
    audio.src = dataUrl;
    audio.volume = this.volume;
    
    return audio;
  }

  private generateToneDataUrl(frequency: number, duration: number, type: OscillatorType): string {
    // This is a simplified implementation
    // In production, you would use actual audio files or a more sophisticated tone generator
    return 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
  }

  public play(soundType: SoundType): void {
    if (!this.isEnabled) return;

    const sound = this.sounds.get(soundType);
    if (sound) {
      // Reset the audio to the beginning
      sound.currentTime = 0;
      
      // Play the sound
      sound.play().catch(error => {
        console.warn('Could not play sound:', error);
      });
    }
  }

  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  public isAudioEnabled(): boolean {
    return this.isEnabled;
  }

  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    
    // Update volume for all sounds
    this.sounds.forEach(sound => {
      sound.volume = this.volume;
    });
  }

  public getVolume(): number {
    return this.volume;
  }

  // Convenience methods for common sound patterns
  public playSuccess(): void {
    this.play('correct');
    setTimeout(() => this.play('achievement'), 200);
  }

  public playError(): void {
    this.play('incorrect');
  }

  public playClick(): void {
    this.play('click');
  }

  public playHover(): void {
    this.play('hover');
  }

  public playNotification(): void {
    this.play('notification');
  }

  public playGameStart(): void {
    this.play('gameStart');
  }

  public playGameEnd(): void {
    this.play('gameEnd');
  }

  public playLevelUp(): void {
    // Play a sequence of tones for level up
    this.play('levelUp');
    setTimeout(() => this.play('achievement'), 300);
  }

  public playTimeWarning(): void {
    this.play('timeWarning');
  }

  // Method to preload all sounds (call this when the app starts)
  public preloadSounds(): Promise<void[]> {
    const loadPromises = Array.from(this.sounds.values()).map(sound => {
      return new Promise<void>((resolve) => {
        if (sound.readyState >= 2) {
          resolve();
        } else {
          sound.addEventListener('canplaythrough', () => resolve(), { once: true });
          sound.addEventListener('error', () => resolve(), { once: true });
        }
      });
    });

    return Promise.all(loadPromises);
  }
}

// Create and export a singleton instance
export const soundService = new SoundService();

// Export the class for testing purposes
export { SoundService };
