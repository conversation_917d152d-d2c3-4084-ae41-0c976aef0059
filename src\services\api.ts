import axios from 'axios';
import type {
  User,
  Question,
  GameSession,
  TriviaWorld,
  Achievement,
  Leaderboard,
  ApiResponse,
  PaginatedResponse,
  QuestionCategory
} from '../types';
import { mockUser, mockDailyQuestion, getRandomQuestion } from './mockData';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
const TRIVIA_API_URL = 'https://opentdb.com/api.php';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth-token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> => {
    // Mock implementation for development
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (email === '<EMAIL>' && password === 'password123') {
          resolve({
            success: true,
            data: {
              user: mockUser,
              token: 'mock-jwt-token-' + Date.now()
            }
          });
        } else {
          reject({
            response: {
              data: {
                error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
              }
            }
          });
        }
      }, 1000);
    });
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> => {
    // Mock implementation for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const newUser: User = {
          ...mockUser,
          id: 'user-' + Date.now(),
          username: userData.username,
          email: userData.email,
          level: 1,
          xp: 0,
          totalScore: 0,
          gamesPlayed: 0,
          streakDays: 0,
          createdAt: new Date().toISOString()
        };

        resolve({
          success: true,
          data: {
            user: newUser,
            token: 'mock-jwt-token-' + Date.now()
          }
        });
      }, 1000);
    });
  },

  logout: async (): Promise<ApiResponse<null>> => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('auth-token');
    return response.data;
  },

  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  getProfile: async (): Promise<ApiResponse<User>> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (updates: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.patch('/auth/profile', updates);
    return response.data;
  },
};

// Questions API
export const questionsAPI = {
  getQuestions: async (params: {
    category?: QuestionCategory;
    difficulty?: 'easy' | 'medium' | 'hard';
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<PaginatedResponse<Question>>> => {
    const response = await api.get('/questions', { params });
    return response.data;
  },

  getQuestionById: async (id: string): Promise<ApiResponse<Question>> => {
    const response = await api.get(`/questions/${id}`);
    return response.data;
  },

  getDailyChallenge: async (): Promise<ApiResponse<Question>> => {
    // Mock implementation for development
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: mockDailyQuestion
        });
      }, 1000);
    });
  },

  getDailyRiddle: async (): Promise<ApiResponse<Question>> => {
    const response = await api.get('/questions/daily-riddle');
    return response.data;
  },

  // External API for additional questions
  getExternalQuestions: async (params: {
    amount: number;
    category?: number;
    difficulty?: 'easy' | 'medium' | 'hard';
    type?: 'multiple' | 'boolean';
  }): Promise<any> => {
    const response = await axios.get(TRIVIA_API_URL, { params });
    return response.data;
  },

  submitQuestion: async (question: Omit<Question, 'id' | 'createdAt'>): Promise<ApiResponse<Question>> => {
    const response = await api.post('/questions', question);
    return response.data;
  },

  rateQuestion: async (questionId: string, rating: number): Promise<ApiResponse<null>> => {
    const response = await api.post(`/questions/${questionId}/rate`, { rating });
    return response.data;
  },
};

// Game API
export const gameAPI = {
  startGame: async (gameData: {
    mode: string;
    category?: QuestionCategory;
    difficulty?: 'easy' | 'medium' | 'hard';
    questionCount?: number;
  }): Promise<ApiResponse<GameSession>> => {
    // Mock implementation for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockSession: GameSession = {
          id: 'session-' + Date.now(),
          userId: mockUser.id,
          mode: gameData.mode as any,
          questions: [mockDailyQuestion],
          currentQuestionIndex: 0,
          answers: [],
          score: 0,
          startTime: new Date().toISOString(),
          status: 'active'
        };
        resolve({
          success: true,
          data: mockSession
        });
      }, 500);
    });
  },

  submitAnswer: async (gameId: string, answer: {
    questionId: string;
    selectedAnswer: number;
    timeSpent: number;
  }): Promise<ApiResponse<{ isCorrect: boolean; pointsEarned: number; explanation?: string }>> => {
    // Mock implementation for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const isCorrect = answer.selectedAnswer === mockDailyQuestion.correctAnswer;
        let pointsEarned = 0;

        if (isCorrect) {
          pointsEarned = mockDailyQuestion.points;
          // Time bonus
          if (answer.timeSpent <= 10) {
            pointsEarned = Math.round(pointsEarned * 1.5);
          }
        }

        resolve({
          success: true,
          data: {
            isCorrect,
            pointsEarned,
            explanation: mockDailyQuestion.explanation
          }
        });
      }, 500);
    });
  },

  endGame: async (gameId: string): Promise<ApiResponse<{
    finalScore: number;
    xpEarned: number;
    achievementsUnlocked: Achievement[];
    newLevel?: number;
  }>> => {
    // Mock implementation for development
    return new Promise((resolve) => {
      setTimeout(() => {
        const finalScore = Math.floor(Math.random() * 200) + 50; // Random score between 50-250
        const xpEarned = Math.floor(finalScore * 0.1);
        const achievementsUnlocked: Achievement[] = [];

        // Randomly unlock an achievement
        if (Math.random() > 0.7) {
          achievementsUnlocked.push({
            id: 'daily-champion',
            name: 'Daily Champion',
            nameAr: 'بطل اليوم',
            description: 'Complete daily challenge',
            descriptionAr: 'أكمل تحدي اليوم',
            icon: '🏆',
            type: 'first-win',
            requirement: 1,
            progress: 1,
            isUnlocked: true,
            unlockedAt: new Date().toISOString(),
            rarity: 'common'
          });
        }

        resolve({
          success: true,
          data: {
            finalScore,
            xpEarned,
            achievementsUnlocked,
            newLevel: mockUser.level + (Math.random() > 0.8 ? 1 : 0)
          }
        });
      }, 1000);
    });
  },

  getGameHistory: async (params: {
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<PaginatedResponse<GameSession>>> => {
    const response = await api.get('/games/history', { params });
    return response.data;
  },

  getGameStats: async (): Promise<ApiResponse<{
    totalGames: number;
    totalScore: number;
    averageScore: number;
    accuracy: number;
    favoriteCategory: QuestionCategory;
  }>> => {
    const response = await api.get('/games/stats');
    return response.data;
  },
};

// Trivia Worlds API
export const worldsAPI = {
  getWorlds: async (): Promise<ApiResponse<TriviaWorld[]>> => {
    const response = await api.get('/worlds');
    return response.data;
  },

  getWorldProgress: async (worldId: string): Promise<ApiResponse<{
    completedQuestions: number;
    totalQuestions: number;
    accuracy: number;
    isUnlocked: boolean;
  }>> => {
    const response = await api.get(`/worlds/${worldId}/progress`);
    return response.data;
  },

  unlockWorld: async (worldId: string): Promise<ApiResponse<TriviaWorld>> => {
    const response = await api.post(`/worlds/${worldId}/unlock`);
    return response.data;
  },
};

// Achievements API
export const achievementsAPI = {
  getAchievements: async (): Promise<ApiResponse<Achievement[]>> => {
    const response = await api.get('/achievements');
    return response.data;
  },

  checkAchievements: async (): Promise<ApiResponse<Achievement[]>> => {
    const response = await api.post('/achievements/check');
    return response.data;
  },
};

// Leaderboards API
export const leaderboardsAPI = {
  getLeaderboard: async (type: 'daily' | 'weekly' | 'monthly' | 'all-time', params?: {
    limit?: number;
    category?: QuestionCategory;
  }): Promise<ApiResponse<Leaderboard>> => {
    const response = await api.get(`/leaderboards/${type}`, { params });
    return response.data;
  },

  getUserRank: async (type: 'daily' | 'weekly' | 'monthly' | 'all-time'): Promise<ApiResponse<{
    rank: number;
    score: number;
    total: number;
  }>> => {
    const response = await api.get(`/leaderboards/${type}/rank`);
    return response.data;
  },
};

// Multiplayer API
export const multiplayerAPI = {
  createRoom: async (settings: {
    maxPlayers: number;
    questionCount: number;
    timePerQuestion: number;
    difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
    categories: QuestionCategory[];
  }): Promise<ApiResponse<{ roomId: string; joinCode: string }>> => {
    const response = await api.post('/multiplayer/rooms', settings);
    return response.data;
  },

  joinRoom: async (roomCode: string): Promise<ApiResponse<{ roomId: string }>> => {
    const response = await api.post('/multiplayer/join', { roomCode });
    return response.data;
  },

  leaveRoom: async (roomId: string): Promise<ApiResponse<null>> => {
    const response = await api.post(`/multiplayer/rooms/${roomId}/leave`);
    return response.data;
  },

  getRoomInfo: async (roomId: string): Promise<ApiResponse<any>> => {
    const response = await api.get(`/multiplayer/rooms/${roomId}`);
    return response.data;
  },
};

// Power-ups API
export const powerUpsAPI = {
  getPowerUps: async (): Promise<ApiResponse<any[]>> => {
    const response = await api.get('/powerups');
    return response.data;
  },

  usePowerUp: async (gameId: string, powerUpId: string): Promise<ApiResponse<{
    effect: any;
    remainingUses: number;
  }>> => {
    const response = await api.post(`/games/${gameId}/powerups/${powerUpId}`);
    return response.data;
  },

  purchasePowerUp: async (powerUpId: string, quantity: number): Promise<ApiResponse<{
    newBalance: number;
    totalOwned: number;
  }>> => {
    const response = await api.post('/powerups/purchase', { powerUpId, quantity });
    return response.data;
  },
};

export default api;
