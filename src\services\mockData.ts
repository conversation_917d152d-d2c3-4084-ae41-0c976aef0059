import type { User, Question, TriviaWorld, Achievement } from '../types';

// Mock user data
export const mockUser: User = {
  id: '1',
  username: 'محمد أحمد',
  email: '<EMAIL>',
  level: 12,
  xp: 11500,
  totalScore: 15420,
  gamesPlayed: 87,
  achievements: [],
  streakDays: 7,
  lastLoginDate: new Date().toISOString(),
  createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
  preferences: {
    soundEnabled: true,
    musicEnabled: true,
    language: 'ar',
    difficulty: 'mixed',
    theme: 'nebula'
  }
};

// Mock daily challenge question
export const mockDailyQuestion: Question = {
  id: 'daily-1',
  text: 'ما هي عاصمة المملكة العربية السعودية؟',
  options: [
    'جدة',
    'الرياض',
    'الدمام',
    'مكة المكرمة'
  ],
  correctAnswer: 1,
  difficulty: 'easy',
  category: 'culture',
  explanation: 'الرياض هي العاصمة الرسمية للمملكة العربية السعودية ومقر الحكومة.',
  timeLimit: 30,
  points: 10,
  tags: ['جغرافيا', 'عواصم', 'السعودية'],
  createdAt: new Date().toISOString()
};

// Mock trivia worlds
export const mockTriviaWorlds: TriviaWorld[] = [
  {
    id: 'history',
    name: 'History World',
    nameAr: 'عالم التاريخ',
    description: 'Explore historical events and figures',
    descriptionAr: 'استكشف الأحداث التاريخية والشخصيات المهمة',
    category: 'history',
    totalQuestions: 50,
    completedQuestions: 50,
    isUnlocked: true,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#d97706',
      secondaryColor: '#92400e',
      accentColor: '#fbbf24',
      backgroundGradient: ['#d97706', '#92400e']
    },
    icon: '🏛️',
    backgroundImage: '/worlds/history.jpg'
  },
  {
    id: 'science',
    name: 'Science World',
    nameAr: 'عالم العلوم',
    description: 'Discover scientific facts and theories',
    descriptionAr: 'اكتشف الحقائق والنظريات العلمية',
    category: 'science',
    totalQuestions: 50,
    completedQuestions: 42,
    isUnlocked: true,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#0ea5e9',
      secondaryColor: '#0284c7',
      accentColor: '#38bdf8',
      backgroundGradient: ['#0ea5e9', '#0284c7']
    },
    icon: '🔬',
    backgroundImage: '/worlds/science.jpg'
  },
  {
    id: 'culture',
    name: 'Culture World',
    nameAr: 'عالم الثقافة',
    description: 'Learn about different cultures and traditions',
    descriptionAr: 'تعلم عن الثقافات والتقاليد المختلفة',
    category: 'culture',
    totalQuestions: 50,
    completedQuestions: 30,
    isUnlocked: true,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#d946ef',
      secondaryColor: '#c026d3',
      accentColor: '#f0abfc',
      backgroundGradient: ['#d946ef', '#c026d3']
    },
    icon: '🎭',
    backgroundImage: '/worlds/culture.jpg'
  },
  {
    id: 'movies',
    name: 'Movies World',
    nameAr: 'عالم السينما',
    description: 'Test your knowledge of films and cinema',
    descriptionAr: 'اختبر معرفتك بالأفلام والسينما',
    category: 'movies',
    totalQuestions: 50,
    completedQuestions: 15,
    isUnlocked: true,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#ef4444',
      secondaryColor: '#dc2626',
      accentColor: '#f87171',
      backgroundGradient: ['#ef4444', '#dc2626']
    },
    icon: '🎬',
    backgroundImage: '/worlds/movies.jpg'
  },
  {
    id: 'technology',
    name: 'Technology World',
    nameAr: 'عالم التكنولوجيا',
    description: 'Explore the world of technology and innovation',
    descriptionAr: 'استكشف عالم التكنولوجيا والابتكار',
    category: 'technology',
    totalQuestions: 50,
    completedQuestions: 0,
    isUnlocked: false,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#10b981',
      secondaryColor: '#059669',
      accentColor: '#34d399',
      backgroundGradient: ['#10b981', '#059669']
    },
    icon: '💻',
    backgroundImage: '/worlds/technology.jpg'
  },
  {
    id: 'mystery',
    name: 'Mystery World',
    nameAr: 'عالم الغموض',
    description: 'Solve puzzles and mysterious questions',
    descriptionAr: 'حل الألغاز والأسئلة الغامضة',
    category: 'mystery',
    totalQuestions: 50,
    completedQuestions: 0,
    isUnlocked: false,
    requiredAccuracy: 0.7,
    theme: {
      primaryColor: '#6366f1',
      secondaryColor: '#4f46e5',
      accentColor: '#818cf8',
      backgroundGradient: ['#6366f1', '#4f46e5']
    },
    icon: '🔮',
    backgroundImage: '/worlds/mystery.jpg'
  }
];

// Mock achievements
export const mockAchievements: Achievement[] = [
  {
    id: 'first-win',
    name: 'First Victory',
    nameAr: 'أول انتصار',
    description: 'Complete your first game',
    descriptionAr: 'أكمل أول لعبة لك',
    icon: '🏆',
    type: 'first-win',
    requirement: 1,
    progress: 1,
    isUnlocked: true,
    unlockedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
    rarity: 'common'
  },
  {
    id: 'speed-demon',
    name: 'Speed Demon',
    nameAr: 'شيطان السرعة',
    description: 'Answer 10 questions in under 5 seconds each',
    descriptionAr: 'أجب على 10 أسئلة في أقل من 5 ثوانٍ لكل سؤال',
    icon: '⚡',
    type: 'speed-demon',
    requirement: 10,
    progress: 7,
    isUnlocked: false,
    rarity: 'rare'
  },
  {
    id: 'perfect-score',
    name: 'Perfect Score',
    nameAr: 'النتيجة المثالية',
    description: 'Get 100% accuracy in a game',
    descriptionAr: 'احصل على دقة 100% في لعبة',
    icon: '💯',
    type: 'perfect-score',
    requirement: 1,
    progress: 1,
    isUnlocked: true,
    unlockedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    rarity: 'epic'
  },
  {
    id: 'streak-master',
    name: 'Streak Master',
    nameAr: 'سيد السلسلة',
    description: 'Maintain a 7-day streak',
    descriptionAr: 'حافظ على سلسلة 7 أيام',
    icon: '🔥',
    type: 'streak-master',
    requirement: 7,
    progress: 7,
    isUnlocked: true,
    unlockedAt: new Date().toISOString(),
    rarity: 'legendary'
  }
];

// Mock questions for different categories
export const mockQuestions: Record<string, Question[]> = {
  history: [
    {
      id: 'hist-1',
      text: 'في أي عام تم فتح القسطنطينية؟',
      options: ['1453', '1492', '1517', '1571'],
      correctAnswer: 0,
      difficulty: 'medium',
      category: 'history',
      explanation: 'تم فتح القسطنطينية على يد السلطان محمد الفاتح عام 1453م.',
      timeLimit: 45,
      points: 20,
      tags: ['تاريخ إسلامي', 'فتوحات'],
      createdAt: new Date().toISOString()
    }
  ],
  science: [
    {
      id: 'sci-1',
      text: 'ما هو الرمز الكيميائي للذهب؟',
      options: ['Go', 'Au', 'Ag', 'Gd'],
      correctAnswer: 1,
      difficulty: 'easy',
      category: 'science',
      explanation: 'الرمز الكيميائي للذهب هو Au من الكلمة اللاتينية Aurum.',
      timeLimit: 30,
      points: 10,
      tags: ['كيمياء', 'عناصر'],
      createdAt: new Date().toISOString()
    }
  ]
};

// Helper function to get random question
export const getRandomQuestion = (category?: string): Question => {
  if (category && mockQuestions[category]) {
    const questions = mockQuestions[category];
    return questions[Math.floor(Math.random() * questions.length)];
  }
  return mockDailyQuestion;
};
