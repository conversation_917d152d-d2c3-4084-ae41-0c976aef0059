{"name": "triviaverse", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.80.6", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}