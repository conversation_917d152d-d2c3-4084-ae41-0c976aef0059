import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../utils';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'card';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  className,
  variant = 'rectangular',
  width,
  height,
  lines = 1
}) => {
  const baseClasses = 'bg-white/10 rounded animate-pulse';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'circular':
        return 'rounded-full';
      case 'rectangular':
        return 'rounded-lg';
      case 'card':
        return 'rounded-xl';
      default:
        return 'rounded-lg';
    }
  };

  const getSize = () => {
    const style: React.CSSProperties = {};
    if (width) style.width = typeof width === 'number' ? `${width}px` : width;
    if (height) style.height = typeof height === 'number' ? `${height}px` : height;
    return style;
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn('space-y-2', className)}>
        {Array.from({ length: lines }).map((_, index) => (
          <motion.div
            key={index}
            className={cn(baseClasses, getVariantClasses())}
            style={{
              width: index === lines - 1 ? '75%' : '100%',
              ...getSize()
            }}
            initial={{ opacity: 0.6 }}
            animate={{ opacity: [0.6, 1, 0.6] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.1
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <motion.div
      className={cn(baseClasses, getVariantClasses(), className)}
      style={getSize()}
      initial={{ opacity: 0.6 }}
      animate={{ opacity: [0.6, 1, 0.6] }}
      transition={{
        duration: 1.5,
        repeat: Infinity
      }}
    />
  );
};

// Predefined skeleton components for common use cases
export const CardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('bg-white/5 rounded-xl p-6 border border-white/10', className)}>
    <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
      <LoadingSkeleton variant="circular" width={48} height={48} />
      <div className="flex-1">
        <LoadingSkeleton variant="text" width="60%" height={16} className="mb-2" />
        <LoadingSkeleton variant="text" width="40%" height={12} />
      </div>
    </div>
    <LoadingSkeleton variant="text" lines={3} className="mb-4" />
    <LoadingSkeleton variant="rectangular" width="100%" height={40} />
  </div>
);

export const QuestionSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('bg-white/10 rounded-2xl p-8 border border-white/20', className)}>
    <div className="flex items-center justify-between mb-8">
      <LoadingSkeleton variant="text" width={200} height={20} />
      <LoadingSkeleton variant="circular" width={60} height={60} />
    </div>
    
    <div className="flex items-start space-x-4 rtl:space-x-reverse mb-8">
      <LoadingSkeleton variant="circular" width={48} height={48} />
      <div className="flex-1">
        <LoadingSkeleton variant="text" lines={2} />
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <LoadingSkeleton
          key={index}
          variant="card"
          height={60}
          className="border border-white/20"
        />
      ))}
    </div>
  </div>
);

export const DashboardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('space-y-8', className)}>
    {/* Welcome Section Skeleton */}
    <LoadingSkeleton variant="card" height={200} />
    
    {/* Stats Grid Skeleton */}
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <LoadingSkeleton key={index} variant="card" height={120} />
      ))}
    </div>
    
    {/* Game Modes Grid Skeleton */}
    <div>
      <LoadingSkeleton variant="text" width={200} height={24} className="mb-6" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <CardSkeleton key={index} />
        ))}
      </div>
    </div>
  </div>
);

export default LoadingSkeleton;
