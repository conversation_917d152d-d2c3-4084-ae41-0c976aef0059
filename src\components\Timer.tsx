import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Clock, Pause, Play } from 'lucide-react';
import { cn } from '../utils';

interface TimerProps {
  initialTime: number;
  onTimeUp: () => void;
  onTick?: (timeRemaining: number) => void;
  isPaused?: boolean;
  showWarning?: boolean;
  warningThreshold?: number;
  className?: string;
}

const Timer: React.FC<TimerProps> = ({
  initialTime,
  onTimeUp,
  onTick,
  isPaused = false,
  showWarning = true,
  warningThreshold = 10,
  className
}) => {
  const [timeRemaining, setTimeRemaining] = useState(initialTime);
  const [isWarning, setIsWarning] = useState(false);

  useEffect(() => {
    setTimeRemaining(initialTime);
  }, [initialTime]);

  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setTimeRemaining((prev) => {
        const newTime = prev - 1;
        
        if (onTick) {
          onTick(newTime);
        }

        if (newTime <= 0) {
          onTimeUp();
          return 0;
        }

        if (showWarning && newTime <= warningThreshold) {
          setIsWarning(true);
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isPaused, onTimeUp, onTick, showWarning, warningThreshold]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    const percentage = (timeRemaining / initialTime) * 100;
    if (percentage > 50) return 'text-green-400';
    if (percentage > 25) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getProgressColor = () => {
    const percentage = (timeRemaining / initialTime) * 100;
    if (percentage > 50) return 'from-green-400 to-green-600';
    if (percentage > 25) return 'from-yellow-400 to-yellow-600';
    return 'from-red-400 to-red-600';
  };

  return (
    <motion.div
      animate={isWarning ? { scale: [1, 1.05, 1] } : { scale: 1 }}
      transition={{ duration: 0.5, repeat: isWarning ? Infinity : 0 }}
      className={cn(
        'flex items-center space-x-3 rtl:space-x-reverse',
        className
      )}
    >
      {/* Timer Icon */}
      <div className={cn(
        'p-2 rounded-lg transition-colors',
        isWarning ? 'bg-red-500/20' : 'bg-white/10'
      )}>
        {isPaused ? (
          <Pause className={cn('w-5 h-5', getTimeColor())} />
        ) : (
          <Clock className={cn('w-5 h-5', getTimeColor())} />
        )}
      </div>

      {/* Time Display */}
      <div className="flex flex-col">
        <span className={cn(
          'text-2xl font-bold font-mono',
          getTimeColor()
        )}>
          {formatTime(timeRemaining)}
        </span>
        
        {/* Progress Bar */}
        <div className="w-24 h-1 bg-white/20 rounded-full overflow-hidden mt-1">
          <motion.div
            className={cn('h-full bg-gradient-to-r', getProgressColor())}
            initial={{ width: '100%' }}
            animate={{ width: `${(timeRemaining / initialTime) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>

      {/* Status Text */}
      {isPaused && (
        <span className="text-xs text-white/60 font-arabic">متوقف</span>
      )}
      {isWarning && !isPaused && (
        <motion.span
          animate={{ opacity: [1, 0.5, 1] }}
          transition={{ duration: 0.5, repeat: Infinity }}
          className="text-xs text-red-400 font-arabic font-semibold"
        >
          الوقت ينفد!
        </motion.span>
      )}
    </motion.div>
  );
};

export default Timer;
