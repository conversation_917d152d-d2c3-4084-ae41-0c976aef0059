<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TriviaVerse - اختبار</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #3730a3 100%);
            color: white;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .hero {
            text-align: center;
            padding: 4rem 0;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 2rem;
        }
        
        h1 {
            font-size: 4rem;
            margin: 0 0 1rem 0;
            font-weight: bold;
        }
        
        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.7;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: scale(1.05);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .feature p {
            opacity: 0.8;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <div class="logo">⭐</div>
            <h1>TriviaVerse</h1>
            <p class="subtitle">مغامرة الأسئلة اللامحدودة</p>
            <p class="description">
                انطلق في رحلة معرفية مثيرة عبر عوالم مختلفة من الأسئلة والتحديات. 
                اختبر معلوماتك، تنافس مع الأصدقاء، واكسب الإنجازات في أكبر مغامرة تعليمية تفاعلية.
            </p>
            
            <div class="buttons">
                <a href="#" class="btn btn-primary">ابدأ المغامرة</a>
                <a href="#" class="btn btn-secondary">تسجيل الدخول</a>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🌍</div>
                <h3>عوالم متنوعة</h3>
                <p>استكشف 6 عوالم مختلفة من الأسئلة والتحديات في التاريخ والعلوم والثقافة والمزيد</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">👥</div>
                <h3>تحديات جماعية</h3>
                <p>تنافس مع أصدقائك في تحديات مثيرة واختبر من الأذكى بينكم</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🏆</div>
                <h3>نظام إنجازات</h3>
                <p>اكسب الشارات والإنجازات واصعد في المستويات مع كل إجابة صحيحة</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3>سباق الزمن</h3>
                <p>اختبر سرعة تفكيرك في تحديات زمنية مثيرة ضد الساعة</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🧠</div>
                <h3>ألغاز ذكية</h3>
                <p>تحدى عقلك بألغاز يومية متجددة وأسئلة تتطلب التفكير العميق</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⭐</div>
                <h3>تحديات يومية</h3>
                <p>سؤال جديد كل يوم مع مكافآت خاصة للمحافظة على نشاطك</p>
            </div>
        </div>
    </div>
    
    <script>
        console.log('TriviaVerse - الصفحة تعمل بنجاح!');
        
        // إضافة تأثيرات بسيطة
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('مرحباً بك في TriviaVerse! الموقع قيد التطوير.');
            });
        });
        
        // تأثير hover للميزات
        document.querySelectorAll('.feature').forEach(feature => {
            feature.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            feature.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
