import React from 'react';

const NewHomePage = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #3730a3 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Hero Section */}
      <div style={{
        padding: '4rem 2rem',
        textAlign: 'center',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Logo */}
        <div style={{
          width: '100px',
          height: '100px',
          background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
          borderRadius: '50%',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '3rem',
          marginBottom: '2rem',
          boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
        }}>
          ⭐
        </div>
        
        {/* Title */}
        <h1 style={{
          fontSize: '4rem',
          fontWeight: 'bold',
          margin: '0 0 1rem 0',
          textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
        }}>
          TriviaVerse
        </h1>
        
        {/* Subtitle */}
        <p style={{
          fontSize: '1.8rem',
          marginBottom: '1rem',
          opacity: 0.9,
          fontWeight: '500'
        }}>
          مغامرة الأسئلة اللامحدودة
        </p>
        
        {/* Description */}
        <p style={{
          fontSize: '1.2rem',
          marginBottom: '3rem',
          opacity: 0.8,
          maxWidth: '700px',
          margin: '0 auto 3rem auto',
          lineHeight: '1.6'
        }}>
          انطلق في رحلة معرفية مثيرة عبر عوالم مختلفة من الأسئلة والتحديات. 
          اختبر معلوماتك، تنافس مع الأصدقاء، واكسب الإنجازات في أكبر مغامرة تعليمية تفاعلية.
        </p>
        
        {/* Buttons */}
        <div style={{
          display: 'flex',
          gap: '1.5rem',
          justifyContent: 'center',
          flexWrap: 'wrap',
          marginBottom: '4rem'
        }}>
          <button style={{
            padding: '1.2rem 2.5rem',
            background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
            color: 'white',
            border: 'none',
            borderRadius: '12px',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            boxShadow: '0 8px 25px rgba(59, 130, 246, 0.4)',
            transition: 'all 0.3s ease',
            transform: 'translateY(0)'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 12px 35px rgba(59, 130, 246, 0.6)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';
          }}>
            🚀 ابدأ المغامرة
          </button>
          
          <button style={{
            padding: '1.2rem 2.5rem',
            background: 'transparent',
            color: 'white',
            border: '2px solid rgba(255, 255, 255, 0.4)',
            borderRadius: '12px',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.target.style.background = 'rgba(255, 255, 255, 0.1)';
            e.target.style.borderColor = 'rgba(255, 255, 255, 0.8)';
          }}
          onMouseOut={(e) => {
            e.target.style.background = 'transparent';
            e.target.style.borderColor = 'rgba(255, 255, 255, 0.4)';
          }}>
            🔑 تسجيل الدخول
          </button>
        </div>
      </div>
      
      {/* Features Section */}
      <div style={{
        padding: '4rem 2rem',
        background: 'rgba(0, 0, 0, 0.2)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <h2 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '1rem'
          }}>
            لماذا TriviaVerse؟
          </h2>
          
          <p style={{
            fontSize: '1.2rem',
            textAlign: 'center',
            opacity: 0.8,
            marginBottom: '3rem',
            maxWidth: '600px',
            margin: '0 auto 3rem auto'
          }}>
            اكتشف عالماً جديداً من التعلم والمتعة مع مجموعة متنوعة من الميزات المصممة لتحفيز عقلك
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: '2rem'
          }}>
            {[
              { icon: '🌍', title: 'عوالم متنوعة', desc: 'استكشف 6 عوالم مختلفة من الأسئلة والتحديات في التاريخ والعلوم والثقافة' },
              { icon: '👥', title: 'تحديات جماعية', desc: 'تنافس مع أصدقائك في تحديات مثيرة واختبر من الأذكى بينكم' },
              { icon: '🏆', title: 'نظام إنجازات', desc: 'اكسب الشارات والإنجازات واصعد في المستويات مع كل إجابة صحيحة' },
              { icon: '⚡', title: 'سباق الزمن', desc: 'اختبر سرعة تفكيرك في تحديات زمنية مثيرة ضد الساعة' },
              { icon: '🧠', title: 'ألغاز ذكية', desc: 'تحدى عقلك بألغاز يومية متجددة وأسئلة تتطلب التفكير العميق' },
              { icon: '⭐', title: 'تحديات يومية', desc: 'سؤال جديد كل يوم مع مكافآت خاصة للمحافظة على نشاطك' }
            ].map((feature, index) => (
              <div key={index} style={{
                background: 'rgba(255, 255, 255, 0.1)',
                padding: '2rem',
                borderRadius: '16px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.4)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
              }}>
                <div style={{
                  fontSize: '3rem',
                  marginBottom: '1rem',
                  textAlign: 'center'
                }}>
                  {feature.icon}
                </div>
                <h3 style={{
                  fontSize: '1.4rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem',
                  textAlign: 'center'
                }}>
                  {feature.title}
                </h3>
                <p style={{
                  opacity: 0.9,
                  lineHeight: '1.6',
                  textAlign: 'center'
                }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* CTA Section */}
      <div style={{
        padding: '4rem 2rem',
        textAlign: 'center'
      }}>
        <div style={{
          maxWidth: '800px',
          margin: '0 auto',
          padding: '3rem',
          background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2))',
          borderRadius: '20px',
          border: '1px solid rgba(255, 255, 255, 0.3)',
          backdropFilter: 'blur(10px)'
        }}>
          <h2 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem'
          }}>
            هل أنت مستعد للتحدي؟
          </h2>
          
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            marginBottom: '2rem'
          }}>
            انضم إلى آلاف اللاعبين واختبر معلوماتك في أكبر مغامرة تعليمية تفاعلية
          </p>
          
          <button style={{
            padding: '1.2rem 3rem',
            background: 'linear-gradient(135deg, #f59e0b, #f97316)',
            color: 'white',
            border: 'none',
            borderRadius: '12px',
            fontSize: '1.3rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            boxShadow: '0 8px 25px rgba(245, 158, 11, 0.4)',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'scale(1.05)';
            e.target.style.boxShadow = '0 12px 35px rgba(245, 158, 11, 0.6)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'scale(1)';
            e.target.style.boxShadow = '0 8px 25px rgba(245, 158, 11, 0.4)';
          }}>
            🎯 ابدأ الآن مجاناً
          </button>
        </div>
      </div>
    </div>
  );
};

export default NewHomePage;
