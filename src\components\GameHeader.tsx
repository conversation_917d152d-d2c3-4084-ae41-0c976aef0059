import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Pause, 
  Play, 
  Settings, 
  Home,
  Volume2,
  VolumeX
} from 'lucide-react';
import { Link } from 'react-router-dom';
import Timer from './Timer';
import { cn } from '../utils';

interface GameHeaderProps {
  title: string;
  subtitle?: string;
  timeRemaining: number;
  totalTime: number;
  isPaused: boolean;
  isSoundEnabled: boolean;
  onPause: () => void;
  onResume: () => void;
  onTimeUp: () => void;
  onSoundToggle: () => void;
  onSettings?: () => void;
  onExit?: () => void;
  showExitConfirmation?: boolean;
  className?: string;
}

const GameHeader: React.FC<GameHeaderProps> = ({
  title,
  subtitle,
  timeRemaining,
  totalTime,
  isPaused,
  isSoundEnabled,
  onPause,
  onResume,
  onTimeUp,
  onSoundToggle,
  onSettings,
  onExit,
  showExitConfirmation = false,
  className
}) => {
  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        'bg-black/20 backdrop-blur-md border-b border-white/10 px-6 py-4',
        className
      )}
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Left Section - Back Button */}
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <Link
            to="/dashboard"
            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          
          <div>
            <h1 className="text-xl font-bold text-white font-arabic">{title}</h1>
            {subtitle && (
              <p className="text-sm text-white/60 font-arabic">{subtitle}</p>
            )}
          </div>
        </div>

        {/* Center Section - Timer */}
        <div className="flex items-center">
          <Timer
            initialTime={totalTime}
            onTimeUp={onTimeUp}
            isPaused={isPaused}
            className="mx-4"
          />
        </div>

        {/* Right Section - Controls */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {/* Sound Toggle */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={onSoundToggle}
            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            {isSoundEnabled ? (
              <Volume2 className="w-5 h-5" />
            ) : (
              <VolumeX className="w-5 h-5" />
            )}
          </motion.button>

          {/* Pause/Resume Button */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={isPaused ? onResume : onPause}
            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            {isPaused ? (
              <Play className="w-5 h-5" />
            ) : (
              <Pause className="w-5 h-5" />
            )}
          </motion.button>

          {/* Settings Button */}
          {onSettings && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={onSettings}
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
            >
              <Settings className="w-5 h-5" />
            </motion.button>
          )}

          {/* Exit Button */}
          {onExit && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={onExit}
              className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200"
            >
              <Home className="w-5 h-5" />
            </motion.button>
          )}
        </div>
      </div>

      {/* Pause Overlay */}
      {isPaused && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center"
          >
            <Pause className="w-16 h-16 text-white/70 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-2 font-arabic">اللعبة متوقفة</h2>
            <p className="text-white/70 mb-6 font-arabic">اضغط على زر التشغيل للمتابعة</p>
            
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onResume}
                className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white font-bold rounded-lg transition-colors font-arabic"
              >
                متابعة اللعب
              </motion.button>
              
              {onExit && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={onExit}
                  className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white font-bold rounded-lg transition-colors font-arabic"
                >
                  إنهاء اللعبة
                </motion.button>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Exit Confirmation Modal */}
      {showExitConfirmation && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center max-w-md mx-4"
          >
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Home className="w-8 h-8 text-red-400" />
            </div>
            
            <h2 className="text-2xl font-bold text-white mb-2 font-arabic">إنهاء اللعبة؟</h2>
            <p className="text-white/70 mb-6 font-arabic">
              ستفقد تقدمك الحالي إذا خرجت الآن. هل أنت متأكد؟
            </p>
            
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onExit}
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white font-bold rounded-lg transition-colors font-arabic"
              >
                نعم، إنهاء
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {}} // This should close the modal
                className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-bold rounded-lg transition-colors font-arabic"
              >
                إلغاء
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </motion.header>
  );
};

export default GameHeader;
